import { useState, useEffect, useRef, useCallback } from 'react';
import type {
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  SearchMessagesResponse,
  CreateConversationRequest,
  GetMessagesParams,
  GetConversationsParams,
  SearchMessagesParams,
} from '@/store/api/chatApiSlice';

export type WebSocketMessageType =
  | 'TEXT_MESSAGE'
  | 'TYPING_INDICATOR'
  | 'READ_RECEIPT'
  | 'JOIN_CONVERSATION'
  | 'LEAVE_CONVERSATION'
  | 'CONNECTION_ERROR'
  | 'CONNECTION_SUCCESS'
  // Data request types
  | 'GET_CONVERSATIONS'
  | 'GET_CONVERSATION'
  | 'GET_MESSAGES'
  | 'CREATE_CONVERSATION'
  | 'SEARCH_MESSAGES'
  | 'MARK_MESSAGES_READ'
  | 'UPDATE_CONVERSATION_STATUS'
  | 'GET_NURSE_INFO'
  | 'GET_UNREAD_COUNT'
  // Data response types
  | 'CONVERSATIONS_DATA'
  | 'CONVERSATION_DATA'
  | 'MESSAGES_DATA'
  | 'CONVERSATION_CREATED'
  | 'SEARCH_RESULTS'
  | 'MESSAGES_MARKED_READ'
  | 'CONVERSATION_STATUS_UPDATED'
  | 'NURSE_INFO_DATA'
  | 'UNREAD_COUNT_DATA'
  | 'ERROR_RESPONSE';

export interface WebSocketMessage {
  type: WebSocketMessageType;
  conversationId?: string;
  senderId?: string;
  senderType?: 'nurse' | 'patient';
  senderName?: string;
  content?: string;
  timestamp?: string;
  metadata?: Record<string, unknown>;
  // Request parameters
  requestId?: string;
  params?: unknown;
  // Response data
  data?: unknown;
  success?: boolean;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface WebSocketStatus {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempt: number;
}

// Data request interfaces
export interface DataRequestOptions {
  timeout?: number;
  retries?: number;
  waitForConnection?: boolean; // Whether to wait for WebSocket connection before sending request
}

export interface PendingRequest {
  requestId: string;
  type: WebSocketMessageType;
  resolve: (data: unknown) => void;
  reject: (error: Error) => void;
  timeout: NodeJS.Timeout;
  retries: number;
}

// Helper function to validate JWT token format and expiration
const validateToken = (token: string): { isValid: boolean; error?: string } => {
  try {
    if (!token) {
      return { isValid: false, error: 'Token is empty' };
    }

    const parts = token.split('.');
    if (parts.length !== 3) {
      return { isValid: false, error: 'Invalid JWT format' };
    }

    // Decode payload to check expiration
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

    if (!payload.exp) {
      return { isValid: false, error: 'Token has no expiration' };
    }

    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      return { isValid: false, error: 'Token has expired' };
    }

    // Check if token expires soon (within 5 minutes)
    if (payload.exp - now < 300) {
      console.warn('Token expires soon, consider refreshing');
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: `Token validation error: ${error.message}` };
  }
};

interface UseWebSocketOptions {
  url: string;
  token: string;
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onOpen?: (event: WebSocketEventMap['open']) => void;
  onClose?: (event: WebSocketEventMap['close']) => void;
  onError?: (event: WebSocketEventMap['error']) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

const useWebSocket = ({
  url,
  token,
  userId,
  userType,
  userName,
  enabled = true,
  autoReconnect = true,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5,
  onOpen,
  onClose,
  onError,
  onMessage,
}: UseWebSocketOptions) => {
  const socketRef = useRef<WebSocket | null>(null);

  const [status, setStatus] = useState<WebSocketStatus>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempt: 0,
  });

  const [activeConversations, setActiveConversations] = useState<string[]>([]);

  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const lastMessageTimestampRef = useRef<Record<string, number>>({});

  const [typingUsers, setTypingUsers] = useState<
    Record<string, { userId: string; userName: string; timestamp: number }>
  >({});

  const typingTimeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Data request management
  const pendingRequestsRef = useRef<Map<string, PendingRequest>>(new Map());
  const requestIdCounterRef = useRef(0);

  const onOpenRef = useRef(onOpen);
  const onCloseRef = useRef(onClose);
  const onErrorRef = useRef(onError);
  const onMessageRef = useRef(onMessage);
  const statusRef = useRef(status);
  const urlRef = useRef(url);
  const tokenRef = useRef(token);
  const userIdRef = useRef(userId);
  const userTypeRef = useRef(userType);
  const userNameRef = useRef(userName);
  const enabledRef = useRef(enabled);
  const autoReconnectRef = useRef(autoReconnect);
  const reconnectIntervalRef = useRef(reconnectInterval);
  const maxReconnectAttemptsRef = useRef(maxReconnectAttempts);
  const activeConversationsRef = useRef(activeConversations);

  useEffect(() => {
    onOpenRef.current = onOpen;
    onCloseRef.current = onClose;
    onErrorRef.current = onError;
    onMessageRef.current = onMessage;
    urlRef.current = url;
    tokenRef.current = token;
    userIdRef.current = userId;
    userTypeRef.current = userType;
    userNameRef.current = userName;
    enabledRef.current = enabled;
    autoReconnectRef.current = autoReconnect;
    reconnectIntervalRef.current = reconnectInterval;
    maxReconnectAttemptsRef.current = maxReconnectAttempts;
  }, [
    onOpen,
    onClose,
    onError,
    onMessage,
    url,
    token,
    userId,
    userType,
    userName,
    enabled,
    autoReconnect,
    reconnectInterval,
    maxReconnectAttempts,
  ]);

  useEffect(() => {
    statusRef.current = status;
  }, [status]);

  useEffect(() => {
    activeConversationsRef.current = activeConversations;
  }, [activeConversations]);

  const clearTimers = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }

    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current);
      connectionTimeoutRef.current = null;
    }

    Object.values(typingTimeoutRef.current).forEach(timeout => {
      clearTimeout(timeout);
    });
  }, []);

  // Helper functions for data requests
  const generateRequestId = useCallback(() => {
    requestIdCounterRef.current += 1;
    return `req_${Date.now()}_${requestIdCounterRef.current}`;
  }, []);

  const cleanupRequest = useCallback((requestId: string) => {
    const request = pendingRequestsRef.current.get(requestId);
    if (request) {
      clearTimeout(request.timeout);
      pendingRequestsRef.current.delete(requestId);
    }
  }, []);

  const rejectAllPendingRequests = useCallback((error: Error) => {
    pendingRequestsRef.current.forEach((request) => {
      clearTimeout(request.timeout);
      request.reject(error);
    });
    pendingRequestsRef.current.clear();
  }, []);

  const connect = useCallback(() => {
    console.debug(
      'Connect called, enabled:',
      enabledRef.current,
      'current status:',
      statusRef.current,
      'socket state:',
      socketRef.current?.readyState
    );

    // More robust connection state checking
    if (!enabledRef.current) {
      console.debug('Connect aborted - WebSocket disabled');
      return;
    }

    if (statusRef.current.connecting) {
      console.debug('Connect aborted - already connecting');
      return;
    }

    if (socketRef.current) {
      const state = socketRef.current.readyState;
      if (state === WebSocket.OPEN) {
        console.debug('Connect aborted - already connected');
        return;
      }
      if (state === WebSocket.CONNECTING) {
        console.debug('Connect aborted - connection in progress');
        return;
      }
      // If socket exists but is closed/closing, clean it up first
      if (state === WebSocket.CLOSED || state === WebSocket.CLOSING) {
        console.debug('Cleaning up previous socket before reconnecting');
        socketRef.current = null;
      }
    }

    clearTimers();

    setStatus(prev => ({ ...prev, connecting: true, error: null }));

    try {
      // Validate required parameters
      if (!urlRef.current) {
        throw new Error('WebSocket URL is required');
      }

      if (!tokenRef.current) {
        throw new Error('Authentication token is required');
      }

      if (!userIdRef.current) {
        throw new Error('User ID is required');
      }

      // Validate token format and expiration
      const tokenValidation = validateToken(tokenRef.current);
      if (!tokenValidation.isValid) {
        throw new Error(`Token validation failed: ${tokenValidation.error}`);
      }

      let wsUrl: string;
      try {
        wsUrl = `${urlRef.current}?token=${encodeURIComponent(tokenRef.current)}&userId=${encodeURIComponent(userIdRef.current)}&userType=${userTypeRef.current}`;

        const debugUrl = wsUrl.replace(
          /token=([^&]{1,10}).*?(&|$)/,
          'token=$1...$2'
        );
        console.debug('Connecting to WebSocket:', debugUrl);
      } catch (urlError) {
        throw new Error(
          `Invalid WebSocket URL or parameters: ${urlError.message}`
        );
      }

      console.debug('Creating new WebSocket connection...');

      // Create WebSocket with error handling for immediate failures
      try {
        socketRef.current = new WebSocket(wsUrl);
      } catch (wsError) {
        throw new Error(`Failed to create WebSocket: ${wsError.message}`);
      }

      // Verify socket was created successfully
      if (!socketRef.current) {
        throw new Error('WebSocket creation failed - socket is null');
      }

      // Set up connection timeout with better error handling
      connectionTimeoutRef.current = setTimeout(() => {
        if (statusRef.current.connecting && !statusRef.current.connected) {
          console.error('WebSocket connection timeout after 10 seconds');

          // Clean up the socket safely
          if (socketRef.current) {
            try {
              // Remove event listeners to prevent race conditions
              socketRef.current.onopen = null;
              socketRef.current.onclose = null;
              socketRef.current.onerror = null;
              socketRef.current.onmessage = null;

              if (socketRef.current.readyState === WebSocket.CONNECTING) {
                socketRef.current.close(1000, 'Connection timeout');
              }
            } catch (error) {
              console.warn('Error during timeout cleanup:', error);
            }
            socketRef.current = null;
          }

          setStatus(prev => ({
            ...prev,
            connecting: false,
            error: 'Connection timeout after 10 seconds',
          }));

          if (
            autoReconnectRef.current &&
            statusRef.current.reconnectAttempt < maxReconnectAttemptsRef.current
          ) {
            scheduleReconnect();
          }
        }
      }, 10000);

      // Enhanced onopen handler
      socketRef.current.onopen = event => {
        console.debug('WebSocket connection established successfully');

        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }

        // Update status to connected
        setStatus({
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempt: 0,
        });

        // Rejoin active conversations
        activeConversationsRef.current.forEach(conversationId => {
          try {
            joinConversation(conversationId);
          } catch (error) {
            console.error(`Error rejoining conversation ${conversationId}:`, error);
          }
        });

        // Call user-provided onOpen callback
        if (onOpenRef.current) {
          try {
            onOpenRef.current(event);
          } catch (error) {
            console.error('Error in onOpen callback:', error);
          }
        }
      };

      // Enhanced onclose handler
      socketRef.current.onclose = event => {
        console.debug(
          `WebSocket connection closed: code=${event.code}, reason=${event.reason || 'none'}, wasClean=${event.wasClean}`
        );

        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }

        // Determine if this was an expected closure
        const isExpectedClosure = event.code === 1000 || event.code === 1001;

        // Update status based on closure reason
        setStatus(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error: isExpectedClosure
            ? null
            : `Connection closed unexpectedly: ${event.code} ${event.reason || 'Unknown reason'}`,
        }));

        // Call user-provided onClose callback
        if (onCloseRef.current) {
          try {
            onCloseRef.current(event);
          } catch (error) {
            console.error('Error in onClose callback:', error);
          }
        }

        // Handle reconnection logic
        const shouldReconnect = autoReconnectRef.current &&
                               !isExpectedClosure &&
                               statusRef.current.reconnectAttempt < maxReconnectAttemptsRef.current;

        if (shouldReconnect) {
          console.debug('Scheduling reconnection due to unexpected closure');
          scheduleReconnect();
        } else if (statusRef.current.reconnectAttempt >= maxReconnectAttemptsRef.current) {
          console.error('Max reconnection attempts reached');
          setStatus(prev => ({
            ...prev,
            error: 'Max reconnection attempts reached. Please refresh the page.',
          }));
        }
      };

      // Enhanced onerror handler
      socketRef.current.onerror = event => {
        console.error('WebSocket error occurred:', {
          type: event.type,
          target: event.target,
          readyState: socketRef.current?.readyState,
          url: socketRef.current?.url
        });

        // Only update error status if we're not already handling a close event
        if (socketRef.current && socketRef.current.readyState !== WebSocket.CLOSED) {
          setStatus(prev => ({
            ...prev,
            error: 'WebSocket connection error - check network connectivity',
          }));
        }

        // Call user-provided onError callback
        if (onErrorRef.current) {
          try {
            onErrorRef.current(event);
          } catch (error) {
            console.error('Error in onError callback:', error);
          }
        }
      };

      socketRef.current.onmessage = event => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);

          switch (message.type) {
            case 'CONNECTION_SUCCESS':
              console.debug('Authentication successful, session established');
              break;

            case 'TYPING_INDICATOR':
              if (
                message.conversationId &&
                message.senderId &&
                message.senderName
              ) {
                if (typingTimeoutRef.current[message.senderId]) {
                  clearTimeout(typingTimeoutRef.current[message.senderId]);
                }

                setTypingUsers(prev => ({
                  ...prev,
                  [message.senderId]: {
                    userId: message.senderId,
                    userName: message.senderName || 'Unknown',
                    timestamp: Date.now(),
                  },
                }));

                typingTimeoutRef.current[message.senderId] = setTimeout(() => {
                  setTypingUsers(prev => {
                    const newState = { ...prev };
                    delete newState[message.senderId!];
                    return newState;
                  });
                }, 3000);
              }
              break;

            case 'READ_RECEIPT':
              break;

            case 'CONNECTION_ERROR':
              console.error(
                'Server reported connection error:',
                message.content
              );
              setStatus(prev => ({
                ...prev,
                error: message.content || 'Server connection error',
              }));
              break;

            // Data response handling
            case 'CONVERSATIONS_DATA':
            case 'CONVERSATION_DATA':
            case 'MESSAGES_DATA':
            case 'CONVERSATION_CREATED':
            case 'SEARCH_RESULTS':
            case 'MESSAGES_MARKED_READ':
            case 'CONVERSATION_STATUS_UPDATED':
            case 'NURSE_INFO_DATA':
            case 'UNREAD_COUNT_DATA':
            case 'ERROR_RESPONSE':
              if (message.requestId) {
                const request = pendingRequestsRef.current.get(message.requestId);
                if (request) {
                  cleanupRequest(message.requestId);
                  if (message.type === 'ERROR_RESPONSE' || !message.success) {
                    request.reject(new Error(message.error || 'Request failed'));
                  } else {
                    request.resolve(message);
                  }
                }
              }
              break;

            default:
              if (onMessageRef.current) {
                onMessageRef.current(message);
              }
          }
        } catch (error) {
          console.error(
            'Error parsing WebSocket message:',
            error,
            'Raw data:',
            event.data
          );
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);

      setStatus({
        connected: false,
        connecting: false,
        error: error.message || 'Failed to create WebSocket connection',
        reconnectAttempt: statusRef.current.reconnectAttempt,
      });

      if (
        autoReconnectRef.current &&
        statusRef.current.reconnectAttempt < maxReconnectAttemptsRef.current
      ) {
        scheduleReconnect();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clearTimers]);

  const scheduleReconnect = useCallback(() => {
    const nextAttempt = statusRef.current.reconnectAttempt + 1;

    setStatus(prev => ({
      ...prev,
      reconnectAttempt: nextAttempt,
    }));

    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }

    const baseDelay = reconnectIntervalRef.current * Math.pow(1.5, nextAttempt);
    const jitter = Math.random() * 0.3 * baseDelay;
    const delay = Math.min(baseDelay + jitter, 60000);

    console.debug(
      `Scheduling reconnection attempt ${nextAttempt} in ${Math.round(delay / 1000)}s`
    );

    reconnectTimerRef.current = setTimeout(() => {
      console.debug(
        `Attempting reconnection ${nextAttempt}/${maxReconnectAttemptsRef.current}`
      );
      connect();
    }, delay);
  }, [connect]);

  const disconnect = useCallback(() => {
    console.debug(
      'Disconnect called, current state:',
      socketRef.current?.readyState
    );

    // Clear all timers first
    clearTimers();

    if (socketRef.current) {
      try {
        // Remove event listeners to prevent race conditions during cleanup
        const socket = socketRef.current;
        socket.onopen = null;
        socket.onclose = null;
        socket.onerror = null;
        socket.onmessage = null;

        // Only close if not already closed or closing
        if (
          socket.readyState === WebSocket.OPEN ||
          socket.readyState === WebSocket.CONNECTING
        ) {
          console.debug('Closing WebSocket connection...');
          socket.close(1000, 'User initiated disconnect');
          console.debug('WebSocket disconnected by user');
        } else {
          console.debug(
            'WebSocket already closed or closing, state:',
            socket.readyState
          );
        }
      } catch (error) {
        console.error('Error closing WebSocket:', error);
      }

      // Clear the reference
      socketRef.current = null;
    }

    setStatus({
      connected: false,
      connecting: false,
      error: null,
      reconnectAttempt: 0,
    });

    setActiveConversations([]);
    setTypingUsers({});

    // Reject all pending requests
    rejectAllPendingRequests(new Error('WebSocket disconnected'));
  }, [clearTimers, rejectAllPendingRequests]);

  const sendMessage = useCallback(
    (message: WebSocketMessage) => {
      if (!socketRef.current) {
        console.debug(
          'WebSocket is not initialized, message not sent:',
          message.type
        );
        return false;
      }

      if (socketRef.current.readyState !== WebSocket.OPEN) {
        console.debug(
          'WebSocket is not connected, current state:',
          socketRef.current.readyState,
          'message type:',
          message.type
        );

        if (
          socketRef.current.readyState === WebSocket.CLOSED &&
          autoReconnectRef.current &&
          enabledRef.current
        ) {
          connect();
        }
        return false;
      }

      try {
        const messageString = JSON.stringify(message);
        socketRef.current.send(messageString);
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    },
    [connect]
  );

  const sendTextMessage = useCallback(
    (
      conversationId: string,
      content: string,
      metadata?: Record<string, unknown>
    ) => {
      const now = Date.now();
      const lastTimestamp =
        lastMessageTimestampRef.current[conversationId] || 0;

      if (now - lastTimestamp < 100) {
        console.warn('Message sending throttled');
        return false;
      }

      lastMessageTimestampRef.current[conversationId] = now;

      return sendMessage({
        type: 'TEXT_MESSAGE',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        content,
        timestamp: new Date().toISOString(),
        metadata,
      });
    },
    [sendMessage]
  );

  const sendTypingIndicator = useCallback(
    (conversationId: string) => {
      const now = Date.now();
      const key = `typing_${conversationId}`;
      const lastTimestamp = lastMessageTimestampRef.current[key] || 0;

      if (now - lastTimestamp < 1000) {
        return false;
      }

      lastMessageTimestampRef.current[key] = now;

      return sendMessage({
        type: 'TYPING_INDICATOR',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const sendReadReceipt = useCallback(
    (conversationId: string) => {
      return sendMessage({
        type: 'READ_RECEIPT',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const joinConversation = useCallback(
    (conversationId: string) => {
      setActiveConversations(prev => {
        if (prev.includes(conversationId)) {
          return prev;
        }
        return [...prev, conversationId];
      });

      return sendMessage({
        type: 'JOIN_CONVERSATION',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  const leaveConversation = useCallback(
    (conversationId: string) => {
      setActiveConversations(prev => prev.filter(id => id !== conversationId));

      return sendMessage({
        type: 'LEAVE_CONVERSATION',
        conversationId,
        senderId: userIdRef.current,
        senderType: userTypeRef.current,
        senderName: userNameRef.current,
        timestamp: new Date().toISOString(),
      });
    },
    [sendMessage]
  );

  // Data request methods
  const sendDataRequest = useCallback(
    <T>(
      type: WebSocketMessageType,
      params?: unknown,
      options: DataRequestOptions = {}
    ): Promise<T> => {
      return new Promise(async (resolve, reject) => {
        const { timeout = 10000, retries = 0, waitForConnection = true } = options;

        // Check if WebSocket is connected
        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
          if (!waitForConnection) {
            reject(new Error('WebSocket is not connected'));
            return;
          }

          // If not connected, try to connect and wait
          console.debug(`WebSocket not connected for ${type} request, attempting to connect...`);

          if (!enabledRef.current) {
            reject(new Error('WebSocket is disabled'));
            return;
          }

          // Attempt to connect if not already connecting
          if (!statusRef.current.connecting && !statusRef.current.connected) {
            connect();
          }

          // Wait for connection with timeout
          const connectionTimeout = Math.min(timeout, 5000); // Max 5 seconds for connection
          const startTime = Date.now();

          while (Date.now() - startTime < connectionTimeout) {
            if (socketRef.current?.readyState === WebSocket.OPEN) {
              console.debug(`WebSocket connected, proceeding with ${type} request`);
              break;
            }

            if (statusRef.current.error) {
              reject(new Error(`WebSocket connection failed: ${statusRef.current.error}`));
              return;
            }

            // Wait 100ms before checking again
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // Final check
          if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket connection timeout - could not establish connection'));
            return;
          }
        }

        const requestId = generateRequestId();

        const timeoutId = setTimeout(() => {
          cleanupRequest(requestId);
          reject(new Error(`Request timeout after ${timeout}ms`));
        }, timeout);

        const request: PendingRequest = {
          requestId,
          type,
          resolve: resolve as (data: unknown) => void,
          reject,
          timeout: timeoutId,
          retries,
        };

        pendingRequestsRef.current.set(requestId, request);

        const message: WebSocketMessage = {
          type,
          requestId,
          params,
          senderId: userIdRef.current,
          senderType: userTypeRef.current,
          timestamp: new Date().toISOString(),
        };

        const success = sendMessage(message);
        if (!success) {
          cleanupRequest(requestId);
          reject(new Error('Failed to send WebSocket message'));
        }
      });
    },
    [sendMessage, generateRequestId, cleanupRequest]
  );

  // Specific data request methods
  const requestConversations = useCallback(
    (params?: GetConversationsParams, options?: DataRequestOptions) => {
      return sendDataRequest<ConversationsResponse>('GET_CONVERSATIONS', params, options);
    },
    [sendDataRequest]
  );

  const requestConversation = useCallback(
    (conversationId: string, options?: DataRequestOptions) => {
      return sendDataRequest<ConversationResponse>('GET_CONVERSATION', { conversationId }, options);
    },
    [sendDataRequest]
  );

  const requestMessages = useCallback(
    (params: GetMessagesParams, options?: DataRequestOptions) => {
      return sendDataRequest<MessagesResponse>('GET_MESSAGES', params, options);
    },
    [sendDataRequest]
  );

  const requestCreateConversation = useCallback(
    (params: CreateConversationRequest, options?: DataRequestOptions) => {
      // Default to waiting for connection for critical operations like creating conversations
      const defaultOptions: DataRequestOptions = { waitForConnection: true, timeout: 15000, ...options };
      return sendDataRequest<ConversationResponse>('CREATE_CONVERSATION', params, defaultOptions);
    },
    [sendDataRequest]
  );

  const requestSearchMessages = useCallback(
    (params: SearchMessagesParams, options?: DataRequestOptions) => {
      return sendDataRequest<SearchMessagesResponse>('SEARCH_MESSAGES', params, options);
    },
    [sendDataRequest]
  );

  const requestMarkMessagesRead = useCallback(
    (conversationId: string, options?: DataRequestOptions) => {
      return sendDataRequest<{ success: boolean }>('MARK_MESSAGES_READ', { conversationId }, options);
    },
    [sendDataRequest]
  );

  const requestUpdateConversationStatus = useCallback(
    (params: { conversationId: string; status: 'active' | 'inactive' | 'archived' }, options?: DataRequestOptions) => {
      return sendDataRequest<ConversationResponse>('UPDATE_CONVERSATION_STATUS', params, options);
    },
    [sendDataRequest]
  );

  const requestNurseInfo = useCallback(
    (nurseId: string, options?: DataRequestOptions) => {
      return sendDataRequest<{ nurse: { id: string; name: string; profileImage?: string } }>('GET_NURSE_INFO', { nurseId }, options);
    },
    [sendDataRequest]
  );

  const requestUnreadCount = useCallback(
    (options?: DataRequestOptions) => {
      return sendDataRequest<{ unreadCount: number }>('GET_UNREAD_COUNT', {}, options);
    },
    [sendDataRequest]
  );

  useEffect(() => {
    if (!status.connected) return;

    const pingInterval = setInterval(() => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        try {
          socketRef.current.send(JSON.stringify({ type: 'PING' }));
        } catch (error) {
          console.warn('Failed to send ping:', error);
        }
      }
    }, 30000);

    return () => clearInterval(pingInterval);
  }, [status.connected]);

  useEffect(() => {
    if (enabled) {
      // Add a small delay to prevent rapid connect/disconnect cycles
      const connectTimer = setTimeout(() => {
        connect();
      }, 100);

      return () => clearTimeout(connectTimer);
    } else {
      // Small delay to allow any pending operations to complete before disconnecting
      const disconnectTimer = setTimeout(() => {
        disconnect();
      }, 50);

      return () => clearTimeout(disconnectTimer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enabled]); // Run when enabled changes

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    status,
    connect,
    disconnect,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
    activeConversations,
    // Data request methods
    requestConversations,
    requestConversation,
    requestMessages,
    requestCreateConversation,
    requestSearchMessages,
    requestMarkMessagesRead,
    requestUpdateConversationStatus,
    requestNurseInfo,
    requestUnreadCount,
  };
};

export default useWebSocket;
