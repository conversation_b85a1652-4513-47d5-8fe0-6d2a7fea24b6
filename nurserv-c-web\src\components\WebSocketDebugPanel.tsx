import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, XCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import useWebSocket from '@/hooks/useWebSocket';
import { testWebSocketConnection, testTokenValidity, testNetworkConnectivity } from '@/utils/websocketTest';

interface WebSocketDebugPanelProps {
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
}

const WebSocketDebugPanel: React.FC<WebSocketDebugPanelProps> = ({
  userId,
  userType,
  userName,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [tokenInfo, setTokenInfo] = useState<{
    isValid: boolean;
    expiresAt?: string;
    error?: string;
  }>({ isValid: false });

  const baseWsUrl = import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws') ? baseWsUrl : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  // Validate token
  useEffect(() => {
    const validateToken = () => {
      try {
        if (!token) {
          setTokenInfo({ isValid: false, error: 'No token found in localStorage' });
          return;
        }

        const parts = token.split('.');
        if (parts.length !== 3) {
          setTokenInfo({ isValid: false, error: 'Invalid JWT format' });
          return;
        }

        const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
        
        if (!payload.exp) {
          setTokenInfo({ isValid: false, error: 'Token has no expiration' });
          return;
        }

        const now = Math.floor(Date.now() / 1000);
        const expiresAt = new Date(payload.exp * 1000).toLocaleString();
        
        if (payload.exp < now) {
          setTokenInfo({ isValid: false, error: 'Token has expired', expiresAt });
          return;
        }

        setTokenInfo({ isValid: true, expiresAt });
      } catch (error) {
        setTokenInfo({ isValid: false, error: `Token validation error: ${error.message}` });
      }
    };

    validateToken();
  }, [token]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]); // Keep last 50 logs
  };

  const {
    status: wsStatus,
    sendTextMessage,
    joinConversation,
    leaveConversation,
  } = useWebSocket({
    url: wsUrl,
    token,
    userId,
    userType,
    userName,
    enabled: isVisible, // Only connect when debug panel is visible
    onOpen: () => addLog('✅ WebSocket connection opened'),
    onClose: (event) => addLog(`❌ WebSocket connection closed: ${event.code} ${event.reason}`),
    onError: (event) => addLog(`🚨 WebSocket error occurred`),
    onMessage: (message) => addLog(`📨 Received: ${message.type}`),
  });

  const testConnection = async () => {
    addLog('🔄 Testing WebSocket connection...');

    try {
      // First test network connectivity
      addLog('📡 Testing network connectivity...');
      const networkTest = await testNetworkConnectivity(baseWsUrl);
      if (networkTest.reachable) {
        addLog('✅ Network connectivity OK');
      } else {
        addLog(`❌ Network connectivity failed: ${networkTest.error}`);
      }

      // Test token validity
      addLog('🔑 Testing token validity...');
      const tokenTest = testTokenValidity(token);
      if (tokenTest.isValid) {
        addLog(`✅ Token is valid (expires: ${tokenTest.expiresAt?.toLocaleString()})`);
      } else {
        addLog(`❌ Token validation failed: ${tokenTest.error}`);
        return;
      }

      // Test WebSocket connection
      addLog('🔌 Testing WebSocket connection...');
      const wsTest = await testWebSocketConnection(wsUrl, token, userId, userType, 10000);
      if (wsTest.success) {
        addLog(`✅ WebSocket connection successful (${wsTest.details?.timeToConnect}ms)`);
      } else {
        addLog(`❌ WebSocket connection failed: ${wsTest.error}`);
        if (wsTest.details) {
          addLog(`📋 Details: readyState=${wsTest.details.readyState}, closeCode=${wsTest.details.closeCode}`);
        }
      }
    } catch (error) {
      addLog(`🚨 Test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testJoinConversation = () => {
    const testConversationId = `test-${Date.now()}`;
    addLog(`🏠 Joining test conversation: ${testConversationId}`);
    joinConversation(testConversationId);
  };

  const testSendMessage = () => {
    const testConversationId = `test-${Date.now()}`;
    addLog(`💬 Sending test message to: ${testConversationId}`);
    sendTextMessage(testConversationId, 'Test message from debug panel');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusColor = () => {
    if (wsStatus.connected) return 'bg-green-500';
    if (wsStatus.connecting) return 'bg-yellow-500';
    if (wsStatus.error) return 'bg-red-500';
    return 'bg-gray-500';
  };

  const getStatusIcon = () => {
    if (wsStatus.connected) return <Wifi className="h-4 w-4" />;
    if (wsStatus.connecting) return <RefreshCw className="h-4 w-4 animate-spin" />;
    return <WifiOff className="h-4 w-4" />;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg"
        >
          🔧 WebSocket Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-hidden">
      <Card className="shadow-xl">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              {getStatusIcon()}
              WebSocket Debug Panel
            </CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Connection Status */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
              <span className="text-sm font-medium">
                {wsStatus.connected ? 'Connected' : wsStatus.connecting ? 'Connecting' : 'Disconnected'}
              </span>
            </div>
            {wsStatus.error && (
              <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                {wsStatus.error}
              </div>
            )}
            {wsStatus.reconnectAttempt > 0 && (
              <div className="text-xs text-yellow-600">
                Reconnect attempt: {wsStatus.reconnectAttempt}
              </div>
            )}
          </div>

          {/* Token Status */}
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {tokenInfo.isValid ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">Token Status</span>
            </div>
            {tokenInfo.error && (
              <div className="text-xs text-red-600">{tokenInfo.error}</div>
            )}
            {tokenInfo.expiresAt && (
              <div className="text-xs text-gray-600">Expires: {tokenInfo.expiresAt}</div>
            )}
          </div>

          {/* Connection Info */}
          <div className="text-xs space-y-1">
            <div><strong>URL:</strong> {wsUrl}</div>
            <div><strong>User:</strong> {userId} ({userType})</div>
            <div><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'None'}</div>
          </div>

          {/* Test Actions */}
          <div className="space-y-2">
            <Button onClick={testConnection} size="sm" variant="outline" className="w-full">
              🔍 Run Full Diagnostic
            </Button>
            <div className="grid grid-cols-2 gap-2">
              <Button onClick={testJoinConversation} size="sm" variant="outline">
                Test Join
              </Button>
              <Button onClick={testSendMessage} size="sm" variant="outline">
                Test Message
              </Button>
            </div>
          </div>

          {/* Logs */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Logs</span>
              <Button onClick={clearLogs} size="sm" variant="ghost" className="h-6 text-xs">
                Clear
              </Button>
            </div>
            <div className="bg-gray-50 rounded p-2 max-h-40 overflow-y-auto text-xs font-mono">
              {logs.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WebSocketDebugPanel;
