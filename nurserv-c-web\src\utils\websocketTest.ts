/**
 * WebSocket Connection Test Utility
 * 
 * This utility helps test WebSocket connectivity and diagnose connection issues.
 */

export interface WebSocketTestResult {
  success: boolean;
  error?: string;
  details?: {
    url: string;
    readyState?: number;
    closeCode?: number;
    closeReason?: string;
    timeToConnect?: number;
  };
}

export const testWebSocketConnection = async (
  url: string,
  token: string,
  userId: string,
  userType: string,
  timeout: number = 10000
): Promise<WebSocketTestResult> => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    let ws: WebSocket | null = null;
    let timeoutId: NodeJS.Timeout;

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (ws) {
        ws.onopen = null;
        ws.onclose = null;
        ws.onerror = null;
        ws.onmessage = null;
        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
          ws.close();
        }
      }
    };

    try {
      // Construct WebSocket URL with parameters
      const wsUrl = `${url}?token=${encodeURIComponent(token)}&userId=${encodeURIComponent(userId)}&userType=${userType}`;
      
      console.log('Testing WebSocket connection to:', wsUrl.replace(/token=([^&]{1,10}).*?(&|$)/, 'token=$1...$2'));
      
      ws = new WebSocket(wsUrl);

      // Set timeout
      timeoutId = setTimeout(() => {
        cleanup();
        resolve({
          success: false,
          error: `Connection timeout after ${timeout}ms`,
          details: {
            url: wsUrl.replace(/token=([^&]{1,10}).*?(&|$)/, 'token=$1...$2'),
            readyState: ws?.readyState,
          }
        });
      }, timeout);

      ws.onopen = (event) => {
        const timeToConnect = Date.now() - startTime;
        console.log(`WebSocket connected successfully in ${timeToConnect}ms`);
        
        cleanup();
        resolve({
          success: true,
          details: {
            url: wsUrl.replace(/token=([^&]{1,10}).*?(&|$)/, 'token=$1...$2'),
            readyState: ws?.readyState,
            timeToConnect
          }
        });
      };

      ws.onerror = (event) => {
        console.error('WebSocket error during test:', event);
        cleanup();
        resolve({
          success: false,
          error: 'WebSocket error occurred',
          details: {
            url: wsUrl.replace(/token=([^&]{1,10}).*?(&|$)/, 'token=$1...$2'),
            readyState: ws?.readyState,
          }
        });
      };

      ws.onclose = (event) => {
        console.log('WebSocket closed during test:', event.code, event.reason);
        cleanup();
        resolve({
          success: false,
          error: `WebSocket closed: ${event.code} ${event.reason || 'No reason provided'}`,
          details: {
            url: wsUrl.replace(/token=([^&]{1,10}).*?(&|$)/, 'token=$1...$2'),
            readyState: ws?.readyState,
            closeCode: event.code,
            closeReason: event.reason
          }
        });
      };

    } catch (error) {
      cleanup();
      resolve({
        success: false,
        error: `Failed to create WebSocket: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: {
          url: url
        }
      });
    }
  });
};

/**
 * Test token validity
 */
export const testTokenValidity = (token: string): { isValid: boolean; error?: string; expiresAt?: Date } => {
  try {
    if (!token) {
      return { isValid: false, error: 'Token is empty' };
    }

    const parts = token.split('.');
    if (parts.length !== 3) {
      return { isValid: false, error: 'Invalid JWT format' };
    }

    // Decode payload
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
    
    if (!payload.exp) {
      return { isValid: false, error: 'Token has no expiration' };
    }

    const now = Math.floor(Date.now() / 1000);
    const expiresAt = new Date(payload.exp * 1000);
    
    if (payload.exp < now) {
      return { isValid: false, error: 'Token has expired', expiresAt };
    }

    return { isValid: true, expiresAt };
  } catch (error) {
    return { isValid: false, error: `Token validation error: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};

/**
 * Test network connectivity to the WebSocket server
 */
export const testNetworkConnectivity = async (baseUrl: string): Promise<{ reachable: boolean; error?: string }> => {
  try {
    // Convert WebSocket URL to HTTP URL for testing
    const httpUrl = baseUrl.replace(/^wss?:\/\//, 'https://').replace(/\/ws$/, '/health');
    
    console.log('Testing network connectivity to:', httpUrl);
    
    const response = await fetch(httpUrl, {
      method: 'GET',
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (response.ok) {
      return { reachable: true };
    } else {
      return { reachable: false, error: `HTTP ${response.status}: ${response.statusText}` };
    }
  } catch (error) {
    return { 
      reachable: false, 
      error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
};
