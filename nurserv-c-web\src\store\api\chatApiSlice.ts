import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const chatApiBaseUrl =
  import.meta.env.VITE_CHAT_API_BASE_URL || 'https://chatapi.nurserv.com/';

export interface User {
  id: string;
  type: 'nurse' | 'patient';
  name: string;
  email?: string;
}

export interface Conversation {
  id: string;
  patientId: string;
  nurseId: string;
  patientName: string;
  nurseName: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  status: 'active' | 'inactive' | 'archived';
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: 'nurse' | 'patient';
  senderName: string;
  content: string;
  type: 'text' | 'image' | 'file';
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
  metadata?: Record<string, unknown>;
}

export interface ConversationResponse {
  success: boolean;
  message?: string;
  data?: {
    conversation: Conversation;
  };
  conversation?: Conversation;
}

export interface ConversationsResponse {
  success: boolean;
  message?: string;
  data?: {
    conversations: Conversation[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  conversations?: Conversation[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface MessagesResponse {
  success: boolean;
  message?: string;
  data?: {
    messages: Message[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  messages?: Message[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface SendMessageResponse {
  success: boolean;
  message?: string;
  data?: {
    message: Message;
  };
  messageData?: Message;
  error?: string;
}

export interface SearchMessagesResponse {
  success: boolean;
  messages: Message[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

export interface WebSocketStatus {
  connected: boolean;
  connectionId?: string;
  error?: string;
}

export interface CreateConversationRequest {
  nurseId: string;
  nurseName: string;
  initialMessage?: string;
}

export interface SendMessageRequest {
  conversationId: string;
  content: string;
  type?: 'text' | 'image' | 'file';
  metadata?: Record<string, unknown>;
}

export interface GetMessagesParams {
  conversationId: string;
  page?: number;
  limit?: number;
}

export interface GetConversationsParams {
  page?: number;
  limit?: number;
  status?: 'active' | 'inactive' | 'archived';
}

export interface SearchMessagesParams {
  conversationId: string;
  query: string;
  page?: number;
  limit?: number;
}

// Note: REST API endpoints have been converted to WebSocket-based operations
// The chatApiSlice is kept minimal for any remaining non-chat API needs
export const chatApiSlice = createApi({
  reducerPath: 'chatApi',
  baseQuery: fetchBaseQuery({
    baseUrl: chatApiBaseUrl,
    prepareHeaders: (headers, { getState: _getState }) => {
      const token = localStorage.getItem('idToken');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Conversation', 'Message', 'WebSocket'],
  endpoints: builder => ({
    // Keep only WebSocket status endpoint as it's still needed for connection monitoring

    getWebSocketStatus: builder.query<WebSocketStatus, void>({
      query: () => ({
        url: '/api/chat/websocket/status',
        method: 'GET',
      }),
      providesTags: ['WebSocket'],
      transformErrorResponse: response => {
        console.error('Error getting WebSocket status:', response);
        return response;
      },
    }),


  }),
});

// Only export the WebSocket status hook as all other chat operations are now WebSocket-based
export const {
  useGetWebSocketStatusQuery,
} = chatApiSlice;
